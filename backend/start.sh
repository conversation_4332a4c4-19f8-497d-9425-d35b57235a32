#!/bin/bash

# GameFlex Backend Development Setup Script
# This script starts the Supabase development environment

set -e

echo "🚀 Starting GameFlex Development Backend..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p volumes/storage
mkdir -p volumes/functions

# Set permissions for volumes (optional - continue if it fails)
echo "🔐 Setting permissions..."
if chmod -R 755 volumes/ 2>/dev/null; then
    echo "✅ Permissions set successfully"
else
    echo "⚠️  Could not set permissions (continuing anyway...)"
fi

# Load environment variables
if [ -f .env ]; then
    echo "📋 Loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ .env file not found. Please create one based on .env.example"
    exit 1
fi

# Check if hosts file is configured for domain names
echo "🔍 Checking hosts file configuration..."
if ! grep -q "# GameFlex Development - START" /etc/hosts 2>/dev/null; then
    echo "⚠️  GameFlex domain names not configured in hosts file!"
    echo "   To enable domain names (api.gameflex.local, studio.gameflex.local, etc.):"
    echo "   Run: sudo ./setup-hosts-linux.sh"
    echo "   Or continue with localhost URLs..."
    echo ""
else
    echo "✅ GameFlex domain names configured"
fi

# Start the services
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 5

# Check if services are healthy
echo "🔍 Checking service health..."

# Check if database is ready
until docker-compose exec -T db pg_isready -U postgres -h localhost; do
    echo "Waiting for database..."
    sleep 2
done

echo "✅ Database is ready!"

# Check if Kong is ready
until curl -f http://localhost:${KONG_HTTP_PORT:-8000}/health > /dev/null 2>&1; do
    echo "Waiting for API Gateway..."
    sleep 2
done

echo "✅ API Gateway is ready!"

# Check if Studio is ready
until curl -f http://localhost:${STUDIO_PORT:-3000} > /dev/null 2>&1; do
    echo "Waiting for Supabase Studio..."
    sleep 2
done

echo "✅ Supabase Studio is ready!"

# Wait a bit for storage initialization to complete
echo "⏳ Waiting for storage initialization..."
sleep 5

# Check if storage initialization completed successfully
if docker-compose ps storage-init | grep -q "Exit 0"; then
    echo "✅ Storage initialization completed successfully!"
elif docker-compose ps storage-init | grep -q "Exited"; then
    echo "⚠️  Storage initialization may have failed. Check logs with: docker-compose logs storage-init"
else
    echo "🔄 Storage initialization is still running..."
fi

echo ""
echo "🎉 GameFlex Development Backend is now running!"
echo ""

# Check if domain names are configured and show appropriate URLs
if grep -q "# GameFlex Development - START" /etc/hosts 2>/dev/null; then
    echo "🎯 GameFlex Services (Domain Names):"
    echo "   📊 Supabase Studio: http://studio.gameflex.local:${STUDIO_PORT:-3000}"
    echo "   🔌 API Gateway: http://api.gameflex.local:${KONG_HTTP_PORT:-8000}"
    echo "   🗄️  Database: db.gameflex.local:${POSTGRES_PORT:-5432}"
else
    echo "🎯 GameFlex Services (Localhost):"
    echo "   📊 Supabase Studio: http://localhost:${STUDIO_PORT:-3000}"
    echo "   🔌 API Gateway: http://localhost:${KONG_HTTP_PORT:-8000}"
    echo "   🗄️  Database: localhost:${POSTGRES_PORT:-5432}"
    echo ""
    echo "💡 To enable domain names, run:"
    echo "   sudo ./setup-hosts-linux.sh"
fi
echo ""
echo "🔑 Development Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: GameFlex123!"
echo ""
echo "🔑 Admin Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: AdminGameFlex123!"
echo ""
echo "🔧 To stop the backend: docker-compose down"
echo "🔧 To view logs: docker-compose logs -f"
echo "🔧 To reset data: docker-compose down -v && ./start.sh"
echo "🔧 To test storage: ./scripts/test-storage.sh"
echo ""
